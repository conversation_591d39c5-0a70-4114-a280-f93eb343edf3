import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/empty_state.dart';

@RoutePage()
class StoreInfoPage extends StatefulWidget {
  const StoreInfoPage({super.key});

  @override
  State<StoreInfoPage> createState() => _StoreInfoPageState();
}

class _StoreInfoPageState extends State<StoreInfoPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  // Track which button is currently selected
  String _selectedButton = 'contacts'; // Options: 'contacts', 'comments'

  // Sample data - replace with actual data models
  List<StoreContact> contacts = [];
  List<StoreComment> comments = [];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _tabController.addListener(_handleTabChange);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _handleTabChange() {
    if (_tabController.indexIsChanging) {
      setState(() {
        // Update the selected button based on tab index
        _selectedButton = _tabController.index == 0 ? 'contacts' : 'comments';
      });
    }
  }

  void _loadData() {
    // TODO: Load actual data from API or local storage
    // For now, using empty lists to show empty state
    setState(() {
      contacts = [];
      comments = [];
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.lightGrey2,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: Text(
          'Store Information',
          style: Theme.of(context).textTheme.montserratNavigationPrimaryMedium.copyWith(
            color: AppColors.black,
            fontWeight: FontWeight.w600,
            fontSize: 18,
          ),
        ),
        iconTheme: const IconThemeData(color: AppColors.black),
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(48),
          child: Container(
            color: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Row(
              children: [
                // Toggle buttons similar to ViewToggleButtons
                Expanded(
                  child: Container(
                    height: 40,
                    decoration: BoxDecoration(
                      color: const Color(0xFFF5F5F5),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: const Color(0xFFE0E0E0),
                        width: 1,
                      ),
                    ),
                    child: Row(
                      children: [
                        // Contacts button
                        _buildToggleButton(
                          context,
                          'Contacts',
                          'contacts',
                          const BorderRadius.only(
                            topLeft: Radius.circular(8),
                            bottomLeft: Radius.circular(8),
                          ),
                        ),
                        // Comments button
                        _buildToggleButton(
                          context,
                          'Comments',
                          'comments',
                          const BorderRadius.only(
                            topRight: Radius.circular(8),
                            bottomRight: Radius.circular(8),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
      body: Container(
        color: AppColors.lightGrey2,
        child: _selectedButton == 'contacts'
            ? _buildContactsList()
            : _buildCommentsList(),
      ),
    );
  }

  Widget _buildToggleButton(
    BuildContext context,
    String text,
    String buttonKey,
    BorderRadius borderRadius,
  ) {
    final bool isSelected = _selectedButton == buttonKey;

    return Expanded(
      child: ElevatedButton(
        onPressed: () {
          setState(() {
            _selectedButton = buttonKey;
            // Update tab controller to match the selection
            if (buttonKey == 'contacts') {
              _tabController.animateTo(0);
            } else {
              _tabController.animateTo(1);
            }
          });
        },
        style: ElevatedButton.styleFrom(
          elevation: 0,
          backgroundColor:
              isSelected ? AppColors.primaryBlue : const Color(0xFFF5F5F5),
          foregroundColor: isSelected ? Colors.white : AppColors.blackTint1,
          shape: RoundedRectangleBorder(
            borderRadius: borderRadius,
            side: const BorderSide(
              color: Colors.grey,
              width: 0,
            ),
          ),
          padding: EdgeInsets.zero,
        ),
        child: Text(
          text,
          style: Theme.of(context)
              .textTheme
              .montserratNavigationPrimaryMedium
              .copyWith(
                color: isSelected ? Colors.white : Colors.black,
                fontWeight: FontWeight.w600,
                fontSize: 12,
              ),
        ),
      ),
    );
  }

  Widget _buildContactsList() {
    if (contacts.isEmpty) {
      return Container(
        color: AppColors.lightGrey2,
        child: const EmptyState(message: 'No contacts available'),
      );
    }

    return Container(
      color: AppColors.lightGrey2,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: contacts.length,
        itemBuilder: (context, index) {
          final contact = contacts[index];
          return _buildContactItem(contact);
        },
      ),
    );
  }

  Widget _buildCommentsList() {
    if (comments.isEmpty) {
      return Container(
        color: AppColors.lightGrey2,
        child: const EmptyState(message: 'No comments available'),
      );
    }

    return Container(
      color: AppColors.lightGrey2,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: comments.length,
        itemBuilder: (context, index) {
          final comment = comments[index];
          return _buildCommentItem(comment);
        },
      ),
    );
  }

  Widget _buildContactItem(StoreContact contact) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Contact avatar
          CircleAvatar(
            radius: 24,
            backgroundColor: AppColors.primaryBlue.withValues(alpha: 0.1),
            child: Text(
              contact.name.isNotEmpty ? contact.name[0].toUpperCase() : 'C',
              style: Theme.of(context).textTheme.montserratNavigationPrimaryMedium.copyWith(
                color: AppColors.primaryBlue,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          const SizedBox(width: 12),
          // Contact details
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  contact.name,
                  style: Theme.of(context).textTheme.montserratNavigationPrimaryMedium.copyWith(
                    fontWeight: FontWeight.w600,
                    fontSize: 16,
                  ),
                ),
                if (contact.role.isNotEmpty) ...[
                  const SizedBox(height: 4),
                  Text(
                    contact.role,
                    style: Theme.of(context).textTheme.montserratNavigationPrimaryMedium.copyWith(
                      color: AppColors.black.withValues(alpha: 0.6),
                      fontSize: 14,
                    ),
                  ),
                ],
                if (contact.phone.isNotEmpty) ...[
                  const SizedBox(height: 4),
                  Text(
                    contact.phone,
                    style: Theme.of(context).textTheme.montserratNavigationPrimaryMedium.copyWith(
                      color: AppColors.black.withValues(alpha: 0.8),
                      fontSize: 14,
                    ),
                  ),
                ],
              ],
            ),
          ),
          // Action buttons
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (contact.phone.isNotEmpty)
                IconButton(
                  onPressed: () => _callContact(contact.phone),
                  icon: const Icon(Icons.phone, color: AppColors.primaryBlue),
                ),
              if (contact.email.isNotEmpty)
                IconButton(
                  onPressed: () => _emailContact(contact.email),
                  icon: const Icon(Icons.email, color: AppColors.primaryBlue),
                ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildCommentItem(StoreComment comment) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Comment header
          Row(
            children: [
              CircleAvatar(
                radius: 16,
                backgroundColor: AppColors.primaryBlue.withValues(alpha: 0.1),
                child: Text(
                  comment.authorName.isNotEmpty ? comment.authorName[0].toUpperCase() : 'U',
                  style: Theme.of(context).textTheme.montserratNavigationPrimaryMedium.copyWith(
                    color: AppColors.primaryBlue,
                    fontWeight: FontWeight.w600,
                    fontSize: 12,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      comment.authorName,
                      style: Theme.of(context).textTheme.montserratNavigationPrimaryMedium.copyWith(
                        fontWeight: FontWeight.w600,
                        fontSize: 14,
                      ),
                    ),
                    Text(
                      comment.timestamp,
                      style: Theme.of(context).textTheme.montserratNavigationPrimaryMedium.copyWith(
                        color: AppColors.black.withValues(alpha: 0.6),
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          // Comment content
          Text(
            comment.content,
            style: Theme.of(context).textTheme.montserratNavigationPrimaryMedium.copyWith(
              fontSize: 14,
              height: 1.4,
            ),
          ),
        ],
      ),
    );
  }

  void _callContact(String phone) {
    // TODO: Implement phone call functionality
    // You can use url_launcher package to make phone calls
    // launch('tel:$phone');
  }

  void _emailContact(String email) {
    // TODO: Implement email functionality
    // You can use url_launcher package to send emails
    // launch('mailto:$email');
  }
}

// Data models for store contacts and comments
class StoreContact {
  final String id;
  final String name;
  final String role;
  final String phone;
  final String email;

  StoreContact({
    required this.id,
    required this.name,
    required this.role,
    required this.phone,
    required this.email,
  });
}

class StoreComment {
  final String id;
  final String content;
  final String authorName;
  final String timestamp;

  StoreComment({
    required this.id,
    required this.content,
    required this.authorName,
    required this.timestamp,
  });
}
